<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('preventivo_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('preventivo_id')->constrained()->onDelete('cascade');
            $table->string('description');
            $table->decimal('cost', 8, 2);
            $table->text('ai_enhanced_description')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('preventivo_items');
    }
};

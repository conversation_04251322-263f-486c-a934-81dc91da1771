<?php

namespace App\Http\Controllers;

use App\Models\Preventivo;
use App\Models\PreventivoItem;
use App\Models\Client;
use App\Models\Project;
use App\Services\OpenAIService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Storage;

class PreventivoController extends Controller
{
    protected OpenAIService $openAIService;

    public function __construct(OpenAIService $openAIService)
    {
        $this->openAIService = $openAIService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Preventivo::with(['client', 'project']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('quote_number', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('client', function ($clientQuery) use ($search) {
                      $clientQuery->where('first_name', 'like', "%{$search}%")
                                  ->orWhere('last_name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('project', function ($projectQuery) use ($search) {
                      $projectQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by client
        if ($request->filled('client_id')) {
            $query->where('client_id', $request->client_id);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $preventivi = $query->orderBy('created_at', 'desc')->paginate(15);
        $clients = Client::orderBy('first_name')->get();

        // For AJAX requests, return only the table content
        if ($request->ajax()) {
            return response()->json([
                'html' => view('preventivi.partials.table', compact('preventivi'))->render(),
                'pagination' => view('pagination.custom', compact('preventivi'))->render(),
                'total' => $preventivi->total(),
            ]);
        }

        return view('preventivi.index', compact('preventivi', 'clients'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $clients = Client::orderBy('first_name')->get();
        return view('preventivi.create', compact('clients'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'client_id' => 'required|exists:clients,id',
            'project_id' => 'required|exists:projects,id',
            'description' => 'required|string',
            'work_items' => 'required|array|min:1',
            'work_items.*.description' => 'required|string',
            'work_items.*.cost' => 'required|numeric|min:0',
        ]);

        try {
            DB::beginTransaction();

            // Create the preventivo
            $preventivo = Preventivo::create([
                'quote_number' => Preventivo::generateQuoteNumber(),
                'client_id' => $validated['client_id'],
                'project_id' => $validated['project_id'],
                'description' => $validated['description'],
                'status' => 'draft',
            ]);

            // Create work items
            foreach ($validated['work_items'] as $item) {
                PreventivoItem::create([
                    'preventivo_id' => $preventivo->id,
                    'description' => $item['description'],
                    'cost' => $item['cost'],
                ]);
            }

            // Calculate total
            $preventivo->calculateTotal();

            DB::commit();

            return redirect()->route('preventivi.show', $preventivo)
                           ->with('success', 'Preventivo creato con successo.');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating preventivo', [
                'error' => $e->getMessage(),
                'data' => $validated
            ]);
            
            return back()->withInput()
                        ->with('error', 'Errore durante la creazione del preventivo.');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Preventivo $preventivo)
    {
        $preventivo->load(['client', 'project', 'items']);
        return view('preventivi.show', compact('preventivo'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Preventivo $preventivo)
    {
        $preventivo->load(['items']);
        $clients = Client::orderBy('first_name')->get();
        $projects = Project::where('client_id', $preventivo->client_id)->get();
        
        return view('preventivi.edit', compact('preventivo', 'clients', 'projects'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Preventivo $preventivo)
    {
        $validated = $request->validate([
            'client_id' => 'required|exists:clients,id',
            'project_id' => 'required|exists:projects,id',
            'description' => 'required|string',
            'status' => 'required|in:draft,sent,accepted,rejected',
            'work_items' => 'required|array|min:1',
            'work_items.*.description' => 'required|string',
            'work_items.*.cost' => 'required|numeric|min:0',
        ]);

        try {
            DB::beginTransaction();

            // Update preventivo
            $preventivo->update([
                'client_id' => $validated['client_id'],
                'project_id' => $validated['project_id'],
                'description' => $validated['description'],
                'status' => $validated['status'],
            ]);

            // Delete existing items and create new ones
            $preventivo->items()->delete();
            
            foreach ($validated['work_items'] as $item) {
                PreventivoItem::create([
                    'preventivo_id' => $preventivo->id,
                    'description' => $item['description'],
                    'cost' => $item['cost'],
                ]);
            }

            // Calculate total
            $preventivo->calculateTotal();

            DB::commit();

            return redirect()->route('preventivi.show', $preventivo)
                           ->with('success', 'Preventivo aggiornato con successo.');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating preventivo', [
                'error' => $e->getMessage(),
                'preventivo_id' => $preventivo->id,
                'data' => $validated
            ]);
            
            return back()->withInput()
                        ->with('error', 'Errore durante l\'aggiornamento del preventivo.');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Preventivo $preventivo)
    {
        try {
            // Delete PDF file if exists
            if ($preventivo->pdf_path && Storage::exists($preventivo->pdf_path)) {
                Storage::delete($preventivo->pdf_path);
            }

            $preventivo->delete();

            return redirect()->route('preventivi.index')
                           ->with('success', 'Preventivo eliminato con successo.');
        } catch (\Exception $e) {
            Log::error('Error deleting preventivo', [
                'error' => $e->getMessage(),
                'preventivo_id' => $preventivo->id
            ]);
            
            return back()->with('error', 'Errore durante l\'eliminazione del preventivo.');
        }
    }

    /**
     * Get projects by client via AJAX
     */
    public function getProjectsByClient(Client $client)
    {
        $projects = $client->projects()->orderBy('name')->get();

        return response()->json([
            'projects' => $projects->map(function ($project) {
                return [
                    'id' => $project->id,
                    'name' => $project->name,
                ];
            })
        ]);
    }

    /**
     * Enhance work items with AI
     */
    public function enhanceWithAI(Preventivo $preventivo)
    {
        try {
            $preventivo->load('items');

            $workItems = $preventivo->items->map(function ($item) {
                return [
                    'description' => $item->description,
                    'cost' => $item->cost,
                ];
            })->toArray();

            $enhancedItems = $this->openAIService->enhanceWorkItems(
                $preventivo->description,
                $workItems
            );

            // Update items with AI enhanced descriptions
            foreach ($enhancedItems as $index => $enhanced) {
                $item = $preventivo->items[$index];
                $item->update([
                    'ai_enhanced_description' => $enhanced['ai_enhanced_description']
                ]);
            }

            $preventivo->update(['ai_processed' => true]);

            return response()->json([
                'success' => true,
                'message' => 'Descrizioni migliorate con AI con successo.',
                'items' => $enhancedItems
            ]);

        } catch (\Exception $e) {
            Log::error('Error enhancing with AI', [
                'error' => $e->getMessage(),
                'preventivo_id' => $preventivo->id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Errore durante il miglioramento con AI.'
            ], 500);
        }
    }

    /**
     * Generate PDF for the preventivo
     */
    public function generatePDF(Preventivo $preventivo)
    {
        try {
            $preventivo->load(['client', 'project', 'items']);

            $pdf = Pdf::loadView('preventivi.pdf', compact('preventivo'));

            $filename = "preventivo_{$preventivo->quote_number}.pdf";
            $path = "preventivi/{$filename}";

            // Save PDF to storage
            Storage::put($path, $pdf->output());

            // Update preventivo with PDF path
            $preventivo->update(['pdf_path' => $path]);

            return response()->json([
                'success' => true,
                'message' => 'PDF generato con successo.',
                'download_url' => route('preventivi.download-pdf', $preventivo)
            ]);

        } catch (\Exception $e) {
            Log::error('Error generating PDF', [
                'error' => $e->getMessage(),
                'preventivo_id' => $preventivo->id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Errore durante la generazione del PDF.'
            ], 500);
        }
    }

    /**
     * Download PDF
     */
    public function downloadPDF(Preventivo $preventivo)
    {
        if (!$preventivo->pdf_path || !Storage::exists($preventivo->pdf_path)) {
            return back()->with('error', 'PDF non trovato.');
        }

        return Storage::download($preventivo->pdf_path, "preventivo_{$preventivo->quote_number}.pdf");
    }
}

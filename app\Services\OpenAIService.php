<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class OpenAIService
{
    private string $apiKey;
    private string $baseUrl = 'https://api.openai.com/v1';

    public function __construct()
    {
        $this->apiKey = config('services.openai.api_key') ?? env('OPENAI_API_KEY');
        
        if (!$this->apiKey) {
            throw new \Exception('OpenAI API key not configured');
        }
    }

    /**
     * Enhance work item descriptions using ChatGPT
     */
    public function enhanceWorkItems(string $mainDescription, array $workItems): array
    {
        try {
            $prompt = $this->buildPrompt($mainDescription, $workItems);
            
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(30)->post($this->baseUrl . '/chat/completions', [
                'model' => 'gpt-3.5-turbo',
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'Sei un esperto consulente IT che aiuta a creare preventivi dettagliati. Fornisci spiegazioni tecniche chiare e professionali in italiano per ogni voce di lavoro.'
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'max_tokens' => 2000,
                'temperature' => 0.7,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $enhancedContent = $data['choices'][0]['message']['content'] ?? '';
                
                return $this->parseEnhancedDescriptions($enhancedContent, $workItems);
            } else {
                Log::error('OpenAI API Error', [
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                return $this->getFallbackDescriptions($workItems);
            }
        } catch (\Exception $e) {
            Log::error('OpenAI Service Error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->getFallbackDescriptions($workItems);
        }
    }

    /**
     * Build the prompt for ChatGPT
     */
    private function buildPrompt(string $mainDescription, array $workItems): string
    {
        $prompt = "Progetto: {$mainDescription}\n\n";
        $prompt .= "Voci di lavoro da dettagliare:\n\n";
        
        foreach ($workItems as $index => $item) {
            $prompt .= ($index + 1) . ". {$item['description']} - €{$item['cost']}\n";
        }
        
        $prompt .= "\nPer favore, fornisci una spiegazione dettagliata e professionale per ogni voce di lavoro. ";
        $prompt .= "Includi aspetti tecnici, benefici per il cliente e giustificazione del costo. ";
        $prompt .= "Formatta la risposta come:\n\n";
        $prompt .= "VOCE 1:\n[Spiegazione dettagliata]\n\n";
        $prompt .= "VOCE 2:\n[Spiegazione dettagliata]\n\n";
        $prompt .= "E così via per tutte le voci.";
        
        return $prompt;
    }

    /**
     * Parse the enhanced descriptions from ChatGPT response
     */
    private function parseEnhancedDescriptions(string $content, array $workItems): array
    {
        $enhanced = [];
        $sections = preg_split('/VOCE \d+:/i', $content);
        
        // Remove the first empty section
        array_shift($sections);
        
        foreach ($workItems as $index => $item) {
            $enhanced[] = [
                'description' => $item['description'],
                'cost' => $item['cost'],
                'ai_enhanced_description' => isset($sections[$index]) 
                    ? trim($sections[$index]) 
                    : $this->getFallbackDescription($item['description'])
            ];
        }
        
        return $enhanced;
    }

    /**
     * Get fallback descriptions when AI fails
     */
    private function getFallbackDescriptions(array $workItems): array
    {
        return array_map(function ($item) {
            return [
                'description' => $item['description'],
                'cost' => $item['cost'],
                'ai_enhanced_description' => $this->getFallbackDescription($item['description'])
            ];
        }, $workItems);
    }

    /**
     * Generate a simple fallback description
     */
    private function getFallbackDescription(string $description): string
    {
        return "Servizio professionale: {$description}. Questa voce include tutte le attività necessarie per completare il lavoro richiesto secondo gli standard di qualità aziendali.";
    }
}
